import React from 'react';
import { useAppState } from '../contexts/AppStateContext.jsx';
import { useLanguage } from '../LanguageContext.jsx';

const RepairManagement = () => {
  const { setCurrentPage } = useAppState();
  const { t, language } = useLanguage();

  return (
    <div className={`repair-page ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="page-header" style={{
        backgroundColor: '#00b9ae',
        color: 'white',
        padding: '20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1>{t('إدارة الإصلاحات')}</h1>
        <button
          onClick={() => setCurrentPage('dashboard')}
          style={{
            backgroundColor: 'transparent',
            color: 'white',
            border: '2px solid white',
            padding: '8px 16px',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          {t('العودة للوحة التحكم')}
        </button>
      </div>

      <div className="page-content" style={{
        padding: '20px',
        minHeight: 'calc(100vh - 100px)'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '10px',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <h2 style={{ color: '#00b9ae', marginBottom: '20px' }}>
            {t('صفحة الإصلاحات')}
          </h2>
          <p style={{ color: '#666', fontSize: '1.1rem' }}>
            {t('هذه الصفحة قيد التطوير. سيتم نقل محتوى الإصلاحات من App.jsx إلى هنا.')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default RepairManagement;
