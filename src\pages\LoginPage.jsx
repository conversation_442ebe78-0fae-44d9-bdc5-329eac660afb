import React, { useState, useEffect } from 'react';
import { useAppState } from '../contexts/AppStateContext.jsx';
import { useLanguage } from '../LanguageContext.jsx';

const LoginPage = () => {
  const { setIsLoggedIn, setCurrentPage, saveLoginStatus, storeSettings } = useAppState();
  const { t, language } = useLanguage();
  const [passcode, setPasscode] = useState('');
  const [error, setError] = useState('');

  const handleLogin = (e) => {
    e.preventDefault();
    
    if (passcode === storeSettings.adminPasscode) {
      setIsLoggedIn(true);
      setCurrentPage('dashboard');
      saveLoginStatus(true, 'dashboard');
      setError('');
    } else {
      setError(t('كلمة المرور غير صحيحة'));
      setPasscode('');
    }
  };

  return (
    <div className={`login-container ${language === 'ar' ? 'rtl' : 'ltr'}`} style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #00b9ae 0%, #498C8A 100%)',
      fontFamily: language === 'ar' ? 'Cairo, sans-serif' : 'Arial, sans-serif'
    }}>
      <div className="login-card" style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '15px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
        width: '100%',
        maxWidth: '400px',
        textAlign: 'center'
      }}>
        <div className="logo-section" style={{ marginBottom: '30px' }}>
          <h1 style={{
            color: '#00b9ae',
            fontSize: '2.5rem',
            fontWeight: 'bold',
            margin: '0 0 10px 0'
          }}>
            iCalDZ
          </h1>
          <p style={{
            color: '#666',
            fontSize: '1.1rem',
            margin: 0
          }}>
            {storeSettings.storeName}
          </p>
        </div>

        <form onSubmit={handleLogin}>
          <div className="input-group" style={{ marginBottom: '20px' }}>
            <label style={{
              display: 'block',
              marginBottom: '8px',
              color: '#333',
              fontWeight: 'bold',
              textAlign: language === 'ar' ? 'right' : 'left'
            }}>
              {t('كلمة المرور')}
            </label>
            <input
              type="password"
              value={passcode}
              onChange={(e) => setPasscode(e.target.value)}
              placeholder={t('أدخل كلمة المرور')}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #ddd',
                borderRadius: '8px',
                fontSize: '1rem',
                textAlign: 'center',
                direction: 'ltr'
              }}
              autoFocus
            />
          </div>

          {error && (
            <div style={{
              color: '#e74c3c',
              backgroundColor: '#ffeaea',
              padding: '10px',
              borderRadius: '5px',
              marginBottom: '20px',
              fontSize: '0.9rem'
            }}>
              {error}
            </div>
          )}

          <button
            type="submit"
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: '#00b9ae',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '1.1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'background-color 0.3s ease'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#008a82'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#00b9ae'}
          >
            {t('دخول')}
          </button>
        </form>

        <div style={{
          marginTop: '30px',
          padding: '15px',
          backgroundColor: '#f8f9fa',
          borderRadius: '8px',
          fontSize: '0.9rem',
          color: '#666'
        }}>
          <p style={{ margin: '0 0 5px 0' }}>
            {t('معلومات المتجر')}:
          </p>
          <p style={{ margin: '0 0 5px 0' }}>
            {t('الهاتف')}: {storeSettings.storePhone}
          </p>
          <p style={{ margin: 0 }}>
            {t('العنوان')}: {storeSettings.storeAddress}
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
