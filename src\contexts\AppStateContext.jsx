import React, { createContext, useContext, useState, useEffect } from 'react';
import { activationManager } from '../activation.js';
import { SoundManager } from '../SoundManager.js';
import { memoryManager } from '../MemoryManager.js';

const AppStateContext = createContext();

export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};

export const AppStateProvider = ({ children }) => {
  // Activation state
  const [isActivated, setIsActivated] = useState(false);
  const [activationChecked, setActivationChecked] = useState(false);

  // System settings state
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [shortcutsEnabled, setShortcutsEnabled] = useState(true);
  const [printerEnabled, setPrinterEnabled] = useState(true);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  // Navigation state
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);

  // User state
  const [currentUser, setCurrentUser] = useState({ role: 'مدير', name: 'المدير' });

  // Store settings
  const [storeSettings, setStoreSettings] = useState({
    storeName: 'iCalDZ Store',
    storeNumber: 'ST001',
    storePhone: '+*********** 456',
    storeAddress: 'الجزائر العاصمة، الجزائر',
    storeLogo: '',
    taxRate: 19,
    currency: 'DZD',
    adminPasscode: '010290'
  });

  // Check login status
  const checkLoginStatus = () => {
    const loginStatus = localStorage.getItem('icaldz-login-status');
    const currentPage = localStorage.getItem('icaldz-current-page');
    return {
      isLoggedIn: loginStatus === 'true',
      page: currentPage || 'dashboard'
    };
  };

  // Save login status and current page
  const saveLoginStatus = (status, page = 'dashboard') => {
    localStorage.setItem('icaldz-login-status', status.toString());
    localStorage.setItem('icaldz-current-page', page);
  };

  // Load settings from localStorage
  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('icaldz-store-settings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setStoreSettings(prev => ({ ...prev, ...parsedSettings }));
      }
    } catch (error) {
      console.error('Error loading store settings:', error);
    }
  };

  // Save settings to localStorage
  const saveSettings = (newSettings) => {
    try {
      localStorage.setItem('icaldz-store-settings', JSON.stringify(newSettings));
      setStoreSettings(newSettings);
    } catch (error) {
      console.error('Error saving store settings:', error);
    }
  };

  // Check activation status on app start
  useEffect(() => {
    const checkActivation = () => {
      const activationStatus = activationManager.checkActivationStatus();
      setIsActivated(activationStatus.activated);
      setActivationChecked(true);

      if (!activationStatus.activated) {
        console.log('Activation required:', activationStatus.reason);
      } else if (activationStatus.type === 'TRIAL') {
        console.log(`Trial activation: ${activationStatus.daysLeft} days left`);
      }
    };

    checkActivation();
  }, []);

  // Initialize system components
  useEffect(() => {
    const initializeSystem = async () => {
      try {
        // Initialize Sound Manager
        await SoundManager.init();
        const soundStatus = SoundManager.getStatus();
        setSoundEnabled(soundStatus.isEnabled);

        // Initialize Memory Manager for long-term stability
        if (memoryManager && !memoryManager.isInitialized) {
          await memoryManager.init();
          console.log('🧠 Memory Manager initialized for system stability');
        }

        // Load system states from localStorage
        const savedPrinterState = localStorage.getItem('printerEnabled');
        if (savedPrinterState !== null) {
          setPrinterEnabled(JSON.parse(savedPrinterState));
        }

        const savedShortcutsState = localStorage.getItem('shortcutsEnabled');
        if (savedShortcutsState !== null) {
          setShortcutsEnabled(JSON.parse(savedShortcutsState));
        }

        const savedNotificationsState = localStorage.getItem('notificationsEnabled');
        if (savedNotificationsState !== null) {
          setNotificationsEnabled(JSON.parse(savedNotificationsState));
        }

        console.log('🎵 Audio system initialized');

        // Play welcome sound
        const welcomeTimeout = setTimeout(() => {
          SoundManager.play('success', { showNotification: false });
        }, 1000);

        // Register timeout with memory manager
        if (memoryManager) {
          memoryManager.registerTimeout(welcomeTimeout, 'welcome_sound');
        }

      } catch (error) {
        console.error('🔇 Failed to initialize system:', error);
      }
    };

    if (isActivated) {
      initializeSystem();
      loadSettings();
    }
  }, [isActivated]);

  // Initialize login status
  useEffect(() => {
    const loginStatus = checkLoginStatus();
    setCurrentPage(loginStatus.isLoggedIn ? loginStatus.page : 'login');
    setIsLoggedIn(loginStatus.isLoggedIn);
  }, []);

  // Scroll to top button functionality
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.pageYOffset || document.documentElement.scrollTop;
      setShowScrollTop(scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle successful activation
  const handleActivationSuccess = (activationData) => {
    setIsActivated(true);
    console.log('Program activated successfully:', activationData);
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const value = {
    // Activation state
    isActivated,
    setIsActivated,
    activationChecked,
    handleActivationSuccess,

    // System settings
    soundEnabled,
    setSoundEnabled,
    shortcutsEnabled,
    setShortcutsEnabled,
    printerEnabled,
    setPrinterEnabled,
    notificationsEnabled,
    setNotificationsEnabled,

    // Navigation
    currentPage,
    setCurrentPage,
    isLoggedIn,
    setIsLoggedIn,
    showScrollTop,
    scrollToTop,

    // User
    currentUser,
    setCurrentUser,

    // Store settings
    storeSettings,
    setStoreSettings,
    saveSettings,
    loadSettings,

    // Auth helpers
    checkLoginStatus,
    saveLoginStatus
  };

  return (
    <AppStateContext.Provider value={value}>
      {children}
    </AppStateContext.Provider>
  );
};
