import React from 'react';
import { useAppState } from '../contexts/AppStateContext.jsx';
import { useLanguage } from '../LanguageContext.jsx';

const Dashboard = () => {
  const { storeSettings } = useAppState();
  const { t, language } = useLanguage();

  return (
    <div className={`dashboard-page ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="page-header" style={{
        backgroundColor: '#00b9ae',
        color: 'white',
        padding: '20px',
        textAlign: 'center'
      }}>
        <h1>{t('لوحة التحكم')}</h1>
        <p>{storeSettings.storeName}</p>
      </div>

      <div className="dashboard-content" style={{
        padding: '20px',
        minHeight: 'calc(100vh - 100px)'
      }}>
        <div className="dashboard-grid" style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}>
          {/* Quick Stats Cards */}
          <div className="stat-card" style={{
            backgroundColor: 'white',
            padding: '20px',
            borderRadius: '10px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            textAlign: 'center'
          }}>
            <h3 style={{ color: '#00b9ae', margin: '0 0 10px 0' }}>
              {t('المبيعات اليومية')}
            </h3>
            <p style={{ fontSize: '2rem', fontWeight: 'bold', margin: 0, color: '#333' }}>
              0 {t('دج')}
            </p>
          </div>

          <div className="stat-card" style={{
            backgroundColor: 'white',
            padding: '20px',
            borderRadius: '10px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            textAlign: 'center'
          }}>
            <h3 style={{ color: '#498C8A', margin: '0 0 10px 0' }}>
              {t('المشتريات')}
            </h3>
            <p style={{ fontSize: '2rem', fontWeight: 'bold', margin: 0, color: '#333' }}>
              0 {t('دج')}
            </p>
          </div>

          <div className="stat-card" style={{
            backgroundColor: 'white',
            padding: '20px',
            borderRadius: '10px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            textAlign: 'center'
          }}>
            <h3 style={{ color: '#46ACC2', margin: '0 0 10px 0' }}>
              {t('الإصلاحات')}
            </h3>
            <p style={{ fontSize: '2rem', fontWeight: 'bold', margin: 0, color: '#333' }}>
              0
            </p>
          </div>
        </div>

        {/* Navigation Menu */}
        <div className="navigation-menu" style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '15px'
        }}>
          <NavigationCard 
            title={t('المبيعات')}
            icon="💰"
            color="#00b9ae"
            page="sales"
          />
          <NavigationCard 
            title={t('المشتريات')}
            icon="📦"
            color="#498C8A"
            page="purchases"
          />
          <NavigationCard 
            title={t('المنتجات')}
            icon="📱"
            color="#46ACC2"
            page="products"
          />
          <NavigationCard 
            title={t('العملاء')}
            icon="👥"
            color="#42F2F7"
            page="customers"
          />
          <NavigationCard 
            title={t('الإصلاحات')}
            icon="🔧"
            color="#00b9ae"
            page="repairs"
          />
          <NavigationCard 
            title={t('التقارير')}
            icon="📊"
            color="#498C8A"
            page="reports"
          />
          <NavigationCard 
            title={t('الإعدادات')}
            icon="⚙️"
            color="#46ACC2"
            page="settings"
          />
        </div>
      </div>
    </div>
  );
};

const NavigationCard = ({ title, icon, color, page }) => {
  const { setCurrentPage, saveLoginStatus } = useAppState();

  const handleClick = () => {
    setCurrentPage(page);
    saveLoginStatus(true, page);
  };

  return (
    <div
      className="nav-card"
      onClick={handleClick}
      style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '10px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        textAlign: 'center',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        border: `3px solid ${color}`
      }}
      onMouseEnter={(e) => {
        e.target.style.transform = 'translateY(-5px)';
        e.target.style.boxShadow = '0 5px 20px rgba(0,0,0,0.2)';
      }}
      onMouseLeave={(e) => {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
      }}
    >
      <div style={{ fontSize: '3rem', marginBottom: '10px' }}>
        {icon}
      </div>
      <h3 style={{ color, margin: 0, fontSize: '1.2rem' }}>
        {title}
      </h3>
    </div>
  );
};

export default Dashboard;
