import React from 'react';
import { useAppState } from '../contexts/AppStateContext.jsx';
import { useLanguage } from '../LanguageContext.jsx';
import ActivationDialog from '../ActivationDialog.jsx';
import LanguageSelectionDialog from '../LanguageSelectionDialog.jsx';
import LoginPage from '../pages/LoginPage.jsx';
import Dashboard from '../pages/Dashboard.jsx';
import SalesManagement from '../pages/SalesManagement.jsx';
import PurchaseManagement from '../pages/PurchaseManagement.jsx';
import ProductManagement from '../pages/ProductManagement.jsx';
import CustomerManagement from '../pages/CustomerManagement.jsx';
import RepairManagement from '../pages/RepairManagement.jsx';
import ReportsPage from '../pages/ReportsPage.jsx';
import SettingsPage from '../pages/SettingsPage.jsx';

const AppRouter = () => {
  const { 
    isActivated, 
    activationChecked, 
    currentPage, 
    isLoggedIn,
    showScrollTop,
    scrollToTop
  } = useAppState();
  
  const { language, showLanguageDialog } = useLanguage();

  // Show activation dialog if not activated
  if (activationChecked && !isActivated) {
    return <ActivationDialog />;
  }

  // Show language selection dialog if needed
  if (showLanguageDialog) {
    return <LanguageSelectionDialog />;
  }

  // Show login page if not logged in
  if (!isLoggedIn) {
    return <LoginPage />;
  }

  // Render current page based on navigation state
  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'sales':
        return <SalesManagement />;
      case 'purchases':
        return <PurchaseManagement />;
      case 'products':
        return <ProductManagement />;
      case 'customers':
        return <CustomerManagement />;
      case 'repairs':
        return <RepairManagement />;
      case 'reports':
        return <ReportsPage />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className={`app-container ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {renderCurrentPage()}
      
      {/* Scroll to top button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="scroll-to-top-btn"
          style={{
            position: 'fixed',
            bottom: '20px',
            right: language === 'ar' ? 'auto' : '20px',
            left: language === 'ar' ? '20px' : 'auto',
            width: '50px',
            height: '50px',
            borderRadius: '50%',
            backgroundColor: '#00b9ae',
            color: 'white',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer',
            zIndex: 1000,
            boxShadow: '0 4px 8px rgba(0,0,0,0.3)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = '#008a82';
            e.target.style.transform = 'scale(1.1)';
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = '#00b9ae';
            e.target.style.transform = 'scale(1)';
          }}
        >
          ↑
        </button>
      )}
    </div>
  );
};

export default AppRouter;
